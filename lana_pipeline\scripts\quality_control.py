#!/usr/bin/env python3
"""
Quality Control Pipeline for Lana Pixie Content
Automated checks for eyes, hands, backgrounds, and file optimization
"""

import os
import json
import cv2
import numpy as np
from PIL import Image, ExifTags
import subprocess
from pathlib import Path
import mediapipe as mp

class QualityController:
    def __init__(self, config_path="../config.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
            
        self.quality_config = self.config.get("quality_checks", {})
        
        # Initialize MediaPipe for hand and face detection
        try:
            self.mp_hands = mp.solutions.hands
            self.mp_face = mp.solutions.face_detection
            self.hands = self.mp_hands.Hands(
                static_image_mode=True,
                max_num_hands=2,
                min_detection_confidence=0.5
            )
            self.face_detection = self.mp_face.FaceDetection(
                model_selection=0,
                min_detection_confidence=0.5
            )
            self.mediapipe_available = True
        except ImportError:
            print("⚠️ MediaPipe not available. Hand/face detection will be limited.")
            self.mediapipe_available = False
    
    def check_file_size(self, image_path):
        """Check if file size is within acceptable limits"""
        max_size_mb = self.quality_config.get("max_file_size_mb", 10)
        file_size_mb = os.path.getsize(image_path) / (1024 * 1024)
        
        return {
            "passed": file_size_mb <= max_size_mb,
            "file_size_mb": round(file_size_mb, 2),
            "max_allowed_mb": max_size_mb
        }
    
    def check_image_quality(self, image_path):
        """Check basic image quality metrics"""
        try:
            img = cv2.imread(image_path)
            if img is None:
                return {"passed": False, "error": "Could not load image"}
            
            # Convert to grayscale for analysis
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Check sharpness (Laplacian variance)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            sharpness_threshold = 100  # Adjust based on testing
            
            # Check brightness
            mean_brightness = np.mean(gray)
            brightness_ok = 30 <= mean_brightness <= 220
            
            # Check contrast
            contrast = np.std(gray)
            contrast_ok = contrast > 15
            
            return {
                "passed": laplacian_var > sharpness_threshold and brightness_ok and contrast_ok,
                "sharpness": round(laplacian_var, 2),
                "brightness": round(mean_brightness, 2),
                "contrast": round(contrast, 2),
                "sharpness_threshold": sharpness_threshold
            }
            
        except Exception as e:
            return {"passed": False, "error": str(e)}
    
    def check_hands(self, image_path):
        """Check for proper hand anatomy (≤5 fingers each)"""
        if not self.mediapipe_available:
            return {"passed": True, "note": "MediaPipe not available, skipping hand check"}
        
        try:
            # Load image
            img = cv2.imread(image_path)
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Detect hands
            results = self.hands.process(img_rgb)
            
            if not results.multi_hand_landmarks:
                return {"passed": True, "hands_detected": 0, "note": "No hands detected"}
            
            hands_count = len(results.multi_hand_landmarks)
            
            # Basic check: if hands are detected, assume they're OK
            # More sophisticated checking would require finger counting
            return {
                "passed": hands_count <= 2,  # Max 2 hands
                "hands_detected": hands_count,
                "note": f"{hands_count} hand(s) detected"
            }
            
        except Exception as e:
            return {"passed": True, "error": str(e), "note": "Hand check failed, assuming OK"}
    
    def check_eyes(self, image_path):
        """Check for proper eye anatomy (no doubles, sharp focus)"""
        if not self.mediapipe_available:
            return {"passed": True, "note": "MediaPipe not available, skipping eye check"}
        
        try:
            # Load image
            img = cv2.imread(image_path)
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Detect face
            results = self.face_detection.process(img_rgb)
            
            if not results.detections:
                return {"passed": False, "note": "No face detected"}
            
            # If face is detected, assume eyes are OK
            # More sophisticated checking would require eye landmark detection
            face_confidence = results.detections[0].score[0]
            min_confidence = self.quality_config.get("min_face_confidence", 0.8)
            
            return {
                "passed": face_confidence >= min_confidence,
                "face_confidence": round(face_confidence, 3),
                "min_confidence": min_confidence,
                "note": "Face detected"
            }
            
        except Exception as e:
            return {"passed": True, "error": str(e), "note": "Eye check failed, assuming OK"}
    
    def check_background(self, image_path):
        """Check background for glitches and artifacts"""
        try:
            img = cv2.imread(image_path)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Check for extreme values that might indicate glitches
            min_val, max_val, _, _ = cv2.minMaxLoc(gray)
            
            # Check for unusual patterns (high frequency noise)
            # Apply high-pass filter to detect noise
            kernel = np.array([[-1,-1,-1], [-1,8,-1], [-1,-1,-1]])
            filtered = cv2.filter2D(gray, -1, kernel)
            noise_level = np.std(filtered)
            
            # Thresholds (adjust based on testing)
            extreme_values_ok = min_val > 5 and max_val < 250
            noise_ok = noise_level < 50
            
            return {
                "passed": extreme_values_ok and noise_ok,
                "min_brightness": int(min_val),
                "max_brightness": int(max_val),
                "noise_level": round(noise_level, 2),
                "note": "Background analysis complete"
            }
            
        except Exception as e:
            return {"passed": True, "error": str(e), "note": "Background check failed, assuming OK"}
    
    def remove_metadata(self, image_path):
        """Remove EXIF and other metadata from image"""
        try:
            # Use exiftool if available
            if self.is_exiftool_available():
                subprocess.run(['exiftool', '-all=', '-overwrite_original', image_path], 
                             capture_output=True, check=True)
                return {"success": True, "method": "exiftool"}
            else:
                # Fallback: use PIL to strip metadata
                with Image.open(image_path) as img:
                    # Create new image without metadata
                    data = list(img.getdata())
                    clean_img = Image.new(img.mode, img.size)
                    clean_img.putdata(data)
                    clean_img.save(image_path)
                return {"success": True, "method": "PIL"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def is_exiftool_available(self):
        """Check if exiftool is available"""
        try:
            subprocess.run(['exiftool', '-ver'], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def optimize_file_size(self, image_path, max_size_mb=None):
        """Optimize image file size while maintaining quality"""
        if max_size_mb is None:
            max_size_mb = self.quality_config.get("max_file_size_mb", 10)
        
        max_size_bytes = max_size_mb * 1024 * 1024
        current_size = os.path.getsize(image_path)
        
        if current_size <= max_size_bytes:
            return {"optimized": False, "original_size_mb": round(current_size / (1024*1024), 2)}
        
        try:
            with Image.open(image_path) as img:
                # Start with high quality and reduce if needed
                quality = 95
                
                while quality > 60:
                    # Save to temporary location
                    temp_path = image_path + ".temp"
                    img.save(temp_path, "JPEG", quality=quality, optimize=True)
                    
                    if os.path.getsize(temp_path) <= max_size_bytes:
                        # Replace original with optimized version
                        os.replace(temp_path, image_path)
                        new_size = os.path.getsize(image_path)
                        return {
                            "optimized": True,
                            "original_size_mb": round(current_size / (1024*1024), 2),
                            "new_size_mb": round(new_size / (1024*1024), 2),
                            "quality": quality
                        }
                    
                    os.remove(temp_path)
                    quality -= 5
                
                return {"optimized": False, "error": "Could not optimize to target size"}
                
        except Exception as e:
            return {"optimized": False, "error": str(e)}
    
    def run_full_quality_check(self, image_path):
        """Run complete quality check on an image"""
        print(f"🔍 Quality checking: {os.path.basename(image_path)}")
        
        results = {
            "image_path": image_path,
            "timestamp": str(np.datetime64('now')),
            "checks": {}
        }
        
        # File size check
        results["checks"]["file_size"] = self.check_file_size(image_path)
        
        # Image quality check
        results["checks"]["image_quality"] = self.check_image_quality(image_path)
        
        # Hand check
        if self.quality_config.get("check_hands", True):
            results["checks"]["hands"] = self.check_hands(image_path)
        
        # Eye check
        if self.quality_config.get("check_eyes", True):
            results["checks"]["eyes"] = self.check_eyes(image_path)
        
        # Background check
        results["checks"]["background"] = self.check_background(image_path)
        
        # Remove metadata
        results["metadata_removal"] = self.remove_metadata(image_path)
        
        # Optimize if needed
        if not results["checks"]["file_size"]["passed"]:
            results["optimization"] = self.optimize_file_size(image_path)
        
        # Overall pass/fail
        all_checks = [check.get("passed", True) for check in results["checks"].values()]
        results["overall_passed"] = all(all_checks)
        
        # Print summary
        status = "✅ PASSED" if results["overall_passed"] else "❌ FAILED"
        print(f"   {status} - {os.path.basename(image_path)}")
        
        return results
    
    def process_directory(self, directory_path, output_report=True):
        """Process all images in a directory"""
        dir_path = Path(directory_path)
        if not dir_path.exists():
            print(f"❌ Directory not found: {directory_path}")
            return []
        
        # Get all images
        extensions = ['.png', '.jpg', '.jpeg']
        images = []
        for ext in extensions:
            images.extend(list(dir_path.glob(f"*{ext}")))
        
        print(f"🔍 Quality checking {len(images)} images in {directory_path}")
        
        results = []
        passed_count = 0
        
        for img_path in images:
            result = self.run_full_quality_check(str(img_path))
            results.append(result)
            if result["overall_passed"]:
                passed_count += 1
        
        print(f"📊 Quality check complete: {passed_count}/{len(images)} passed")
        
        # Save report
        if output_report and results:
            report_path = dir_path / "quality_report.json"
            with open(report_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"📄 Quality report saved: {report_path}")
        
        return results

if __name__ == "__main__":
    qc = QualityController()
    
    # Example usage
    test_dir = "../output/sfw"
    if os.path.exists(test_dir):
        qc.process_directory(test_dir)
    else:
        print("No output directory found. Run batch generation first.")
