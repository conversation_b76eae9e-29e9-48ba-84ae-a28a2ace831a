#!/usr/bin/env python3
"""
Lana Pi<PERSON>e Master Pipeline
Complete automated workflow for consistent character generation
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from pathlib import Path

# Add scripts directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts'))

from batch_generator import LanaPixiePipeline
from facefusion_processor import FaceFusionProcessor
from quality_control import QualityController

class MasterPipeline:
    def __init__(self, config_path="config.json"):
        self.config_path = config_path
        self.load_config()
        self.setup_logging()
        
    def load_config(self):
        """Load pipeline configuration"""
        with open(self.config_path, 'r') as f:
            self.config = json.load(f)
    
    def setup_logging(self):
        """Setup logging directory"""
        log_dir = Path("./logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = log_dir / f"pipeline_{timestamp}.log"
        
    def log(self, message):
        """Log message to file and console"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a') as f:
            f.write(log_message + "\n")
    
    def check_prerequisites(self):
        """Check if all required components are available"""
        self.log("🔍 Checking prerequisites...")
        
        issues = []
        
        # Check if WebUI is running
        try:
            import requests
            response = requests.get(f"{self.config['webui_url']}/sdapi/v1/options", timeout=5)
            if response.status_code != 200:
                issues.append("Stable Diffusion WebUI not responding")
        except Exception as e:
            issues.append(f"Cannot connect to WebUI: {e}")
        
        # Check if LoRA exists
        lora_path = f"./models/Lora/{self.config['lora_name']}.safetensors"
        if not os.path.exists(lora_path):
            issues.append(f"LoRA not found: {lora_path}")
        
        # Check reference images
        ref_dir = Path(self.config['ref_images_dir'])
        if not ref_dir.exists():
            issues.append(f"Reference images directory not found: {ref_dir}")
        
        # Check FaceFusion
        facefusion_path = Path(self.config['facefusion_path'])
        if not facefusion_path.exists():
            issues.append(f"FaceFusion not found: {facefusion_path}")
        
        if issues:
            self.log("❌ Prerequisites check failed:")
            for issue in issues:
                self.log(f"   - {issue}")
            return False
        else:
            self.log("✅ All prerequisites satisfied")
            return True
    
    def run_generation_phase(self, prompt_count, sfw_ratio):
        """Run the image generation phase"""
        self.log(f"🎨 Starting generation phase: {prompt_count} images ({sfw_ratio:.0%} SFW)")
        
        try:
            pipeline = LanaPixiePipeline(self.config_path)
            results = pipeline.run_batch_generation(prompt_count, sfw_ratio)
            
            successful = sum(1 for r in results if r["status"] == "success")
            self.log(f"✅ Generation phase complete: {successful}/{prompt_count} successful")
            
            return successful > 0
            
        except Exception as e:
            self.log(f"❌ Generation phase failed: {e}")
            return False
    
    def run_facefusion_phase(self):
        """Run the FaceFusion refinement phase"""
        self.log("🔄 Starting FaceFusion phase...")
        
        try:
            processor = FaceFusionProcessor(self.config_path)
            processor.auto_process_pipeline_output("./output")
            
            self.log("✅ FaceFusion phase complete")
            return True
            
        except Exception as e:
            self.log(f"❌ FaceFusion phase failed: {e}")
            return False
    
    def run_quality_control_phase(self):
        """Run the quality control phase"""
        self.log("🔍 Starting quality control phase...")
        
        try:
            qc = QualityController(self.config_path)
            
            # Process SFW images
            sfw_dir = "./output/sfw"
            if os.path.exists(sfw_dir):
                sfw_results = qc.process_directory(sfw_dir)
                sfw_passed = sum(1 for r in sfw_results if r["overall_passed"])
                self.log(f"   SFW: {sfw_passed}/{len(sfw_results)} passed quality check")
            
            # Process NSFW images
            nsfw_dir = "./output/nsfw"
            if os.path.exists(nsfw_dir):
                nsfw_results = qc.process_directory(nsfw_dir)
                nsfw_passed = sum(1 for r in nsfw_results if r["overall_passed"])
                self.log(f"   NSFW: {nsfw_passed}/{len(nsfw_results)} passed quality check")
            
            self.log("✅ Quality control phase complete")
            return True
            
        except Exception as e:
            self.log(f"❌ Quality control phase failed: {e}")
            return False
    
    def archive_results(self):
        """Archive successful results"""
        self.log("📦 Archiving results...")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_dir = Path(f"./output/archive/batch_{timestamp}")
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy successful images to archive
            import shutil
            
            for content_type in ["sfw", "nsfw"]:
                source_dir = Path(f"./output/{content_type}")
                if source_dir.exists():
                    target_dir = archive_dir / content_type
                    target_dir.mkdir(exist_ok=True)
                    
                    # Copy images and metadata
                    for file_path in source_dir.glob("*.png"):
                        shutil.copy2(file_path, target_dir)
                    
                    for file_path in source_dir.glob("*.json"):
                        shutil.copy2(file_path, target_dir)
            
            self.log(f"✅ Results archived to: {archive_dir}")
            return True
            
        except Exception as e:
            self.log(f"❌ Archiving failed: {e}")
            return False
    
    def run_full_pipeline(self, prompt_count=50, sfw_ratio=0.6, skip_facefusion=False, skip_quality_check=False):
        """Run the complete pipeline"""
        self.log("🚀 Starting Lana Pixie Master Pipeline")
        self.log(f"📊 Configuration: {prompt_count} images, {sfw_ratio:.0%} SFW")
        
        start_time = time.time()
        
        # Check prerequisites
        if not self.check_prerequisites():
            self.log("❌ Pipeline aborted due to prerequisite failures")
            return False
        
        # Phase 1: Generation
        if not self.run_generation_phase(prompt_count, sfw_ratio):
            self.log("❌ Pipeline aborted due to generation failure")
            return False
        
        # Phase 2: FaceFusion (optional)
        if not skip_facefusion:
            if not self.run_facefusion_phase():
                self.log("⚠️ FaceFusion failed, continuing without face refinement")
        else:
            self.log("⏭️ Skipping FaceFusion phase")
        
        # Phase 3: Quality Control (optional)
        if not skip_quality_check:
            if not self.run_quality_control_phase():
                self.log("⚠️ Quality control failed, continuing without QC")
        else:
            self.log("⏭️ Skipping quality control phase")
        
        # Phase 4: Archive
        self.archive_results()
        
        # Summary
        elapsed_time = time.time() - start_time
        self.log(f"🎉 Pipeline complete! Total time: {elapsed_time/60:.1f} minutes")
        self.log(f"📄 Log saved to: {self.log_file}")
        
        return True
    
    def quick_test(self):
        """Run a quick test with minimal images"""
        self.log("🧪 Running quick test pipeline...")
        return self.run_full_pipeline(prompt_count=5, sfw_ratio=0.8, skip_facefusion=True)

def main():
    parser = argparse.ArgumentParser(description="Lana Pixie Master Pipeline")
    parser.add_argument("--count", type=int, default=50, help="Number of images to generate")
    parser.add_argument("--sfw-ratio", type=float, default=0.6, help="Ratio of SFW images (0.0-1.0)")
    parser.add_argument("--skip-facefusion", action="store_true", help="Skip FaceFusion phase")
    parser.add_argument("--skip-quality-check", action="store_true", help="Skip quality control phase")
    parser.add_argument("--test", action="store_true", help="Run quick test with 5 images")
    parser.add_argument("--config", default="config.json", help="Configuration file path")
    
    args = parser.parse_args()
    
    # Initialize pipeline
    pipeline = MasterPipeline(args.config)
    
    if args.test:
        success = pipeline.quick_test()
    else:
        success = pipeline.run_full_pipeline(
            prompt_count=args.count,
            sfw_ratio=args.sfw_ratio,
            skip_facefusion=args.skip_facefusion,
            skip_quality_check=args.skip_quality_check
        )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
