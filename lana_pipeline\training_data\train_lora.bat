@echo off
echo Starting Lana Pixie LoRA Training...

echo 📝 Training configuration:
echo   - Images: ./training_data/images
echo   - Output: ./output_lora
echo   - Steps: 2000
echo   - Learning Rate: 1e-4
echo   - Resolution: 768x768

echo.
echo ⚠️ Manual training required:
echo 1. Use your preferred LoRA training tool (Kohya_ss, Dreambooth, etc.)
echo 2. Point it to the prepared images in: ./training_data/images
echo 3. Use the captions in: ./training_data/captions
echo 4. Set trigger word: 'lana pixie'
echo 5. Train for 2000-3000 steps at 1e-4 learning rate

echo.
echo ✅ Training data preparation complete!
pause
