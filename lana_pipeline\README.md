# Lana Pixie Consistent Character Pipeline

Complete automated workflow for generating consistent character content using Stable Diffusion WebUI Forge + FaceFusion.

## 🎯 Features

- **Automated Batch Generation**: Generate 50-100 images with single command
- **SFW/NSFW Content Control**: Configurable ratio and separate prompt templates
- **Face Consistency**: FaceFusion integration for 25-35% face refinement
- **Quality Control**: Automated checks for eyes, hands, backgrounds, file size
- **Metadata Management**: Automatic EXIF removal and file optimization
- **Archival System**: Organized storage with timestamps and metadata
- **Video Generation Ready**: Frame sequences for 8-12 second teasers

## 📁 Directory Structure

```
lana_pipeline/
├── config.json              # Main configuration
├── run_pipeline.py          # Master pipeline script
├── setup.py                 # Setup and dependency installer
├── scripts/
│   ├── batch_generator.py   # Core generation logic
│   ├── facefusion_processor.py  # Face refinement
│   └── quality_control.py   # Quality checks and optimization
├── output/
│   ├── sfw/                 # SFW generated images
│   ├── nsfw/                # NSFW generated images
│   └── archive/             # Timestamped archives
├── prompts/                 # Generated prompt CSV files
└── logs/                    # Pipeline execution logs
```

## 🚀 Quick Start

### 1. Setup
```bash
cd lana_pipeline
python setup.py
```

### 2. Quick Test (5 images)
```bash
python run_pipeline.py --test
```

### 3. Full Pipeline (50 images)
```bash
python run_pipeline.py --count 50 --sfw-ratio 0.6
```

### 4. Windows Batch Scripts
- `quick_test.bat` - Test with 5 images
- `run_full_pipeline.bat` - 50 images, 60% SFW
- `run_sfw_only.bat` - 30 SFW images only

## ⚙️ Configuration

Edit `config.json` to customize:

```json
{
  "lora_name": "lana_pixie_sdxl_improved",
  "lora_strength": 0.8,
  "base_model": "cyberrealisticXL_v60.safetensors",
  "steps": 30,
  "cfg_scale": 7.0,
  "width": 768,
  "height": 1024,
  "facefusion_strength": 0.3,
  "quality_checks": {
    "max_file_size_mb": 10,
    "min_face_confidence": 0.8
  }
}
```

## 📝 Prompt Templates

### SFW Examples
- `soft daylight bedroom, cozy knit sweater, natural makeup, candid`
- `golden hour lighting, casual outfit, warm smile, relaxed pose`
- `studio lighting, elegant dress, professional headshot`

### NSFW Examples
- `dim golden hour, lace lingerie, seductive pose, cinematic lighting`
- `bedroom setting, silk sheets, intimate lighting, artistic nude`
- `boudoir photography, soft shadows, elegant pose, sensual mood`

## 🔧 Command Line Options

```bash
python run_pipeline.py [OPTIONS]

Options:
  --count INT           Number of images to generate (default: 50)
  --sfw-ratio FLOAT     Ratio of SFW images 0.0-1.0 (default: 0.6)
  --skip-facefusion     Skip FaceFusion refinement phase
  --skip-quality-check  Skip quality control phase
  --test               Run quick test with 5 images
  --config PATH        Custom configuration file
```

## 🎨 Pipeline Phases

### Phase 1: Generation
- Loads LoRA and base model
- Generates prompts from templates
- Creates images via WebUI API
- Saves with metadata and timestamps

### Phase 2: FaceFusion (Optional)
- Selects best reference image
- Applies face refinement at 25-35% strength
- Checks for alignment artifacts
- Keeps original if fusion fails

### Phase 3: Quality Control (Optional)
- File size optimization
- Hand anatomy checking (≤5 fingers)
- Eye sharpness verification
- Background artifact detection
- EXIF metadata removal

### Phase 4: Archive
- Organizes by content type (SFW/NSFW)
- Creates timestamped archives
- Preserves generation metadata
- Generates quality reports

## 📊 Quality Gates

Each image passes through automated checks:

- ✅ **File Size**: ≤10MB (configurable)
- ✅ **Face Detection**: Confidence ≥80%
- ✅ **Hand Anatomy**: ≤2 hands detected
- ✅ **Eye Sharpness**: No doubles or blur
- ✅ **Background**: No obvious glitches
- ✅ **Metadata**: EXIF data removed

## 🎬 Video Generation

For 8-12 second teasers:

1. Generate 12-16 consecutive frames
2. Use same LoRA with slight prompt variations
3. Apply FaceFusion to maintain consistency
4. Assemble with FFMPEG:

```bash
ffmpeg -framerate 12 -i frame_%04d.png -c:v libx264 -pix_fmt yuv420p lana_teaser.mp4
```

## 📈 Performance

**Typical Cycle Times** (RTX 4090):
- 50 images: ~25 minutes
- 100 images: ~40 minutes
- With FaceFusion: +50% time
- With Quality Control: +10% time

## 🔧 Troubleshooting

### WebUI Connection Issues
```bash
# Check if WebUI is running
curl http://127.0.0.1:7860/sdapi/v1/options
```

### LoRA Not Loading
- Verify file exists in `models/Lora/`
- Check filename matches config
- Ensure WebUI has API enabled

### FaceFusion Errors
- Check FaceFusion installation
- Verify reference images exist
- Reduce fusion strength if artifacts appear

### Quality Check Failures
- Install MediaPipe: `pip install mediapipe`
- Install exiftool for metadata removal
- Adjust quality thresholds in config

## 📋 Requirements

- Stable Diffusion WebUI Forge
- Python 3.8+
- 8GB+ VRAM (recommended)
- FaceFusion (optional)
- Reference images (49 available)

## 🔄 Workflow Integration

### Daily Routine (15 minutes):
1. `python run_pipeline.py --count 20` (5 min)
2. Review quality report (5 min)
3. Archive approved content (5 min)

### Weekly Optimization:
1. Add new reference images
2. Update prompt templates
3. Retrain LoRA if needed
4. Analyze audience feedback

## 📄 Output Files

Each generation creates:
- `image_id_timestamp_hash.png` - Generated image
- `image_id_timestamp_hash_metadata.json` - Generation parameters
- `quality_report.json` - Quality check results
- `batch_prompts.csv` - Prompt archive

## 🎯 Success Metrics

- **Generation Success Rate**: >90%
- **Quality Pass Rate**: >80%
- **Face Consistency**: Visual similarity maintained
- **File Optimization**: <10MB per image
- **Time Efficiency**: <30 seconds per image

## 🔮 Future Enhancements

- [ ] Advanced pose similarity detection
- [ ] Automated prompt optimization
- [ ] Real-time quality scoring
- [ ] Integration with posting automation
- [ ] Advanced video generation (AnimateDiff)
- [ ] Custom training data curation

---

**Ready to generate consistent, high-quality Lana Pixie content at scale!** 🚀
