{"_class_name": "AutoencoderKL", "_diffusers_version": "0.30.0.dev0", "_name_or_path": "../checkpoints/flux-dev", "act_fn": "silu", "block_out_channels": [128, 256, 512, 512], "down_block_types": ["DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D"], "force_upcast": true, "in_channels": 3, "latent_channels": 16, "latents_mean": null, "latents_std": null, "layers_per_block": 2, "mid_block_add_attention": true, "norm_num_groups": 32, "out_channels": 3, "sample_size": 1024, "scaling_factor": 0.3611, "shift_factor": 0.1159, "up_block_types": ["UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D"], "use_post_quant_conv": false, "use_quant_conv": false}