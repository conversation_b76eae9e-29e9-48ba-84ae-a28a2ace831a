#!/usr/bin/env python3
"""
FaceFusion Integration for Lana Pixie Pipeline
Automated face refinement at 25-35% strength
"""

import os
import json
import subprocess
import random
from pathlib import Path
from PIL import Image
import cv2
import numpy as np

class FaceFusionProcessor:
    def __init__(self, config_path="../config.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        self.facefusion_path = self.config.get("facefusion_path", "../facefusion")
        self.ref_images_dir = self.config.get("ref_images_dir")
        self.strength = self.config.get("facefusion_strength", 0.3)
        
        # Get reference images
        self.ref_images = self.get_reference_images()
        
    def get_reference_images(self):
        """Get list of reference images for face swapping"""
        ref_dir = Path(self.ref_images_dir)
        if not ref_dir.exists():
            print(f"❌ Reference directory not found: {ref_dir}")
            return []
            
        # Get all image files
        extensions = ['.png', '.jpg', '.jpeg', '.webp']
        ref_images = []
        
        for ext in extensions:
            ref_images.extend(list(ref_dir.glob(f"*{ext}")))
            ref_images.extend(list(ref_dir.glob(f"*{ext.upper()}")))
        
        # Filter for good reference images (prefer close-ups and clear faces)
        good_refs = []
        for img_path in ref_images:
            if self.is_good_reference(img_path):
                good_refs.append(str(img_path))
                
        print(f"📸 Found {len(good_refs)} good reference images")
        return good_refs
    
    def is_good_reference(self, image_path):
        """Check if image is suitable as reference (clear face, good quality)"""
        try:
            # Basic file size check (avoid very small images)
            file_size = os.path.getsize(image_path)
            if file_size < 50000:  # Less than 50KB probably too small
                return False
                
            # Check image dimensions
            with Image.open(image_path) as img:
                width, height = img.size
                if width < 256 or height < 256:  # Too small
                    return False
                    
            # TODO: Add face detection to ensure clear face is present
            # For now, assume all images are good
            return True
            
        except Exception as e:
            print(f"⚠️ Error checking reference image {image_path}: {e}")
            return False
    
    def select_best_reference(self, target_image_path):
        """Select best reference image based on pose/angle similarity"""
        if not self.ref_images:
            return None
            
        # For now, randomly select a reference image
        # TODO: Implement pose similarity detection
        return random.choice(self.ref_images)
    
    def run_facefusion(self, source_path, target_path, output_path, strength=None):
        """Run FaceFusion with specified parameters"""
        if strength is None:
            strength = self.strength
            
        # Ensure paths are absolute
        source_path = os.path.abspath(source_path)
        target_path = os.path.abspath(target_path)
        output_path = os.path.abspath(output_path)
        
        # FaceFusion command
        cmd = [
            "python", "facefusion.py",
            "--source", source_path,
            "--target", target_path,
            "--output", output_path,
            "--face-swapper-model", "inswapper_128",
            "--face-enhancer-model", "gfpgan_1.4",
            "--face-enhancer-blend", str(int(strength * 100)),
            "--output-quality", "95",
            "--keep-temp"
        ]
        
        try:
            # Change to FaceFusion directory
            original_cwd = os.getcwd()
            os.chdir(self.facefusion_path)
            
            print(f"🔄 Running FaceFusion: {source_path} -> {target_path}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            os.chdir(original_cwd)
            
            if result.returncode == 0:
                print(f"✅ FaceFusion completed: {output_path}")
                return True
            else:
                print(f"❌ FaceFusion failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ FaceFusion timed out for {target_path}")
            return False
        except Exception as e:
            print(f"❌ FaceFusion error: {e}")
            return False
        finally:
            os.chdir(original_cwd)
    
    def check_face_alignment(self, image_path):
        """Check if face alignment is good (no artifacts)"""
        try:
            # Load image
            img = cv2.imread(image_path)
            if img is None:
                return False
                
            # Convert to RGB
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # TODO: Implement face detection and alignment checking
            # For now, basic checks:
            
            # Check for obvious artifacts (very dark or very bright regions)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            mean_brightness = np.mean(gray)
            
            # Reject if too dark or too bright
            if mean_brightness < 30 or mean_brightness > 220:
                return False
                
            # Check for extreme contrast (possible artifacts)
            contrast = np.std(gray)
            if contrast < 10 or contrast > 80:
                return False
                
            return True
            
        except Exception as e:
            print(f"⚠️ Error checking face alignment: {e}")
            return False
    
    def process_image(self, target_image_path, output_dir=None):
        """Process a single image with FaceFusion"""
        if not os.path.exists(target_image_path):
            print(f"❌ Target image not found: {target_image_path}")
            return None
            
        # Select best reference image
        source_image = self.select_best_reference(target_image_path)
        if not source_image:
            print(f"❌ No reference images available")
            return None
            
        # Setup output path
        if output_dir is None:
            output_dir = os.path.dirname(target_image_path)
            
        target_name = Path(target_image_path).stem
        output_path = os.path.join(output_dir, f"{target_name}_facefused.png")
        
        # Run FaceFusion
        success = self.run_facefusion(source_image, target_image_path, output_path)
        
        if success and os.path.exists(output_path):
            # Check quality
            if self.check_face_alignment(output_path):
                print(f"✅ Face fusion successful: {output_path}")
                return output_path
            else:
                print(f"⚠️ Face alignment issues detected, keeping original")
                os.remove(output_path)
                return target_image_path
        else:
            print(f"❌ Face fusion failed, keeping original")
            return target_image_path
    
    def process_batch(self, image_directory, output_directory=None):
        """Process all images in a directory"""
        image_dir = Path(image_directory)
        if not image_dir.exists():
            print(f"❌ Image directory not found: {image_dir}")
            return []
            
        if output_directory is None:
            output_directory = image_directory
            
        # Get all images
        extensions = ['.png', '.jpg', '.jpeg']
        images = []
        for ext in extensions:
            images.extend(list(image_dir.glob(f"*{ext}")))
            
        print(f"🔄 Processing {len(images)} images with FaceFusion...")
        
        results = []
        for img_path in images:
            # Skip if already processed
            if "_facefused" in img_path.name:
                continue
                
            result = self.process_image(str(img_path), output_directory)
            if result:
                results.append(result)
                
        print(f"✅ FaceFusion batch complete: {len(results)} images processed")
        return results
    
    def auto_process_pipeline_output(self, pipeline_output_dir):
        """Automatically process pipeline output with FaceFusion"""
        output_dir = Path(pipeline_output_dir)
        
        # Process SFW images
        sfw_dir = output_dir / "sfw"
        if sfw_dir.exists():
            print(f"🔄 Processing SFW images...")
            self.process_batch(str(sfw_dir))
            
        # Process NSFW images  
        nsfw_dir = output_dir / "nsfw"
        if nsfw_dir.exists():
            print(f"🔄 Processing NSFW images...")
            self.process_batch(str(nsfw_dir))

if __name__ == "__main__":
    processor = FaceFusionProcessor()
    
    # Example usage
    test_image = "../output/sfw/test_image.png"
    if os.path.exists(test_image):
        result = processor.process_image(test_image)
        print(f"Result: {result}")
    else:
        print("No test image found. Run batch generation first.")
