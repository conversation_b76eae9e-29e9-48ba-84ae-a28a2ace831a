{"_class_name": "AutoencoderKL", "_diffusers_version": "0.29.0.dev0", "act_fn": "silu", "block_out_channels": [128, 256, 512, 512], "down_block_types": ["DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D"], "force_upcast": true, "in_channels": 3, "latent_channels": 16, "latents_mean": null, "latents_std": null, "layers_per_block": 2, "norm_num_groups": 32, "out_channels": 3, "sample_size": 1024, "scaling_factor": 1.5305, "shift_factor": 0.0609, "up_block_types": ["UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D"], "use_post_quant_conv": false, "use_quant_conv": false}