#!/bin/bash
# Lana Pixie LoRA Training Script
# Run this in the WebUI environment

echo "🚀 Starting Lana Pixie LoRA Training..."

# Set environment variables
export MODEL_NAME="lana_pixie_v2"
export INSTANCE_DIR="./training_data/images"
export OUTPUT_DIR="./output_lora"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Training command (you'll need to adapt this to your specific training setup)
echo "📝 Training configuration:"
echo "  - Images: ./training_data/images"
echo "  - Output: ./output_lora"
echo "  - Steps: 2000"
echo "  - Learning Rate: 1e-4"
echo "  - Resolution: 768x768"

echo ""
echo "⚠️ Manual training required:"
echo "1. Use your preferred LoRA training tool (Kohya_ss, Dreambooth, etc.)"
echo "2. Point it to the prepared images in: ./training_data/images"
echo "3. Use the captions in: ./training_data/captions"
echo "4. Set trigger word: 'lana pixie'"
echo "5. Train for 2000-3000 steps at 1e-4 learning rate"

echo ""
echo "✅ Training data preparation complete!"
