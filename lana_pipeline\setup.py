#!/usr/bin/env python3
"""
Setup script for Lana Pixie Pipeline
Installs required dependencies and configures the environment
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def install_requirements():
    """Install required Python packages"""
    requirements = [
        "requests",
        "pillow",
        "opencv-python",
        "numpy",
        "mediapipe"
    ]
    
    print("📦 Installing required packages...")
    for package in requirements:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    
    return True

def check_webui_connection():
    """Check if Stable Diffusion WebUI is accessible"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:7860/sdapi/v1/options", timeout=5)
        if response.status_code == 200:
            print("✅ Stable Diffusion WebUI is running")
            return True
        else:
            print("⚠️ WebUI responded but with error status")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to Web<PERSON>: {e}")
        print("   Make sure Stable Diffusion WebUI is running on http://127.0.0.1:7860")
        return False

def check_lora_exists():
    """Check if Lana Pixie LoRA exists"""
    lora_path = "../models/Lora/lana_pixie_sdxl_improved.safetensors"
    if os.path.exists(lora_path):
        print("✅ Lana Pixie LoRA found")
        return True
    else:
        print(f"❌ LoRA not found: {lora_path}")
        print("   Make sure the LoRA file is in the correct location")
        return False

def check_facefusion():
    """Check if FaceFusion is available"""
    facefusion_path = "../facefusion"
    if os.path.exists(facefusion_path):
        print("✅ FaceFusion directory found")
        return True
    else:
        print(f"⚠️ FaceFusion not found: {facefusion_path}")
        print("   FaceFusion integration will be disabled")
        return False

def check_reference_images():
    """Check if reference images are available"""
    ref_path = "C:/Users/<USER>/Documents/AI_OnlyFans/Lana_Pixie/LORA"
    if os.path.exists(ref_path):
        # Count images
        extensions = ['.png', '.jpg', '.jpeg', '.webp']
        image_count = 0
        for ext in extensions:
            image_count += len(list(Path(ref_path).glob(f"*{ext}")))
            image_count += len(list(Path(ref_path).glob(f"*{ext.upper()}")))
        
        print(f"✅ Found {image_count} reference images")
        return True
    else:
        print(f"❌ Reference images not found: {ref_path}")
        return False

def create_batch_scripts():
    """Create convenient batch scripts for Windows"""
    
    # Quick test script
    test_script = """@echo off
echo Running Lana Pixie Quick Test...
python run_pipeline.py --test
pause
"""
    
    with open("quick_test.bat", "w") as f:
        f.write(test_script)
    
    # Full pipeline script
    full_script = """@echo off
echo Running Lana Pixie Full Pipeline...
python run_pipeline.py --count 50 --sfw-ratio 0.6
pause
"""
    
    with open("run_full_pipeline.bat", "w") as f:
        f.write(full_script)
    
    # SFW only script
    sfw_script = """@echo off
echo Running SFW-only Pipeline...
python run_pipeline.py --count 30 --sfw-ratio 1.0
pause
"""
    
    with open("run_sfw_only.bat", "w") as f:
        f.write(sfw_script)
    
    print("✅ Created batch scripts:")
    print("   - quick_test.bat (5 images for testing)")
    print("   - run_full_pipeline.bat (50 images, 60% SFW)")
    print("   - run_sfw_only.bat (30 SFW images only)")

def main():
    print("🚀 Lana Pixie Pipeline Setup")
    print("=" * 40)
    
    # Install requirements
    if not install_requirements():
        print("❌ Setup failed during package installation")
        return False
    
    print("\n🔍 Checking system requirements...")
    
    # Check components
    checks = [
        ("WebUI Connection", check_webui_connection()),
        ("LoRA File", check_lora_exists()),
        ("FaceFusion", check_facefusion()),
        ("Reference Images", check_reference_images())
    ]
    
    print("\n📊 System Check Results:")
    for name, result in checks:
        status = "✅" if result else "❌"
        print(f"   {status} {name}")
    
    # Create batch scripts
    print("\n📝 Creating convenience scripts...")
    create_batch_scripts()
    
    # Summary
    critical_checks = [checks[0][1], checks[1][1], checks[3][1]]  # WebUI, LoRA, Ref Images
    
    if all(critical_checks):
        print("\n🎉 Setup complete! You can now run the pipeline.")
        print("\nQuick start:")
        print("   1. Run 'quick_test.bat' to test with 5 images")
        print("   2. Run 'run_full_pipeline.bat' for full batch generation")
        print("   3. Or use 'python run_pipeline.py --help' for custom options")
        return True
    else:
        print("\n⚠️ Setup completed with warnings.")
        print("Some components are missing but basic functionality should work.")
        print("Check the failed items above and fix them for full functionality.")
        return True

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
