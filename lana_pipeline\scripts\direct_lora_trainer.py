#!/usr/bin/env python3
"""
Direct LoRA Training Script using Diffusers
Trains a new LoRA model using the prepared training data
"""

import os
import sys
import json
import torch
from pathlib import Path
from PIL import Image
import argparse
from tqdm import tqdm
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from diffusers import StableDiffusionXLPipeline, UNet2DConditionModel, AutoencoderKL
    from diffusers.loaders import LoraLoaderMixin
    from transformers import CLIPTextModel, CLIPTokenizer
    import torch.nn.functional as F
    from torch.utils.data import Dataset, DataLoader
    from accelerate import Accelerator
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Some dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False

class LoRATrainingDataset(Dataset):
    def __init__(self, images_dir, captions_dir, tokenizer, size=768):
        self.images_dir = Path(images_dir)
        self.captions_dir = Path(captions_dir)
        self.tokenizer = tokenizer
        self.size = size
        
        # Get all image files
        self.image_files = list(self.images_dir.glob("*.jpg"))
        self.image_files.extend(list(self.images_dir.glob("*.png")))
        
        logger.info(f"Found {len(self.image_files)} training images")
    
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx):
        # Load image
        image_path = self.image_files[idx]
        image = Image.open(image_path).convert("RGB")
        image = image.resize((self.size, self.size), Image.Resampling.LANCZOS)
        
        # Convert to tensor
        image = torch.tensor(np.array(image)).float() / 255.0
        image = image.permute(2, 0, 1)  # HWC to CHW
        
        # Load caption
        caption_path = self.captions_dir / f"{image_path.stem}.txt"
        if caption_path.exists():
            with open(caption_path, 'r') as f:
                caption = f.read().strip()
        else:
            caption = "lana pixie, beautiful woman"
        
        # Tokenize caption
        tokens = self.tokenizer(
            caption,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            "pixel_values": image,
            "input_ids": tokens.input_ids.squeeze(),
            "attention_mask": tokens.attention_mask.squeeze()
        }

class DirectLoRATrainer:
    def __init__(self, config_path="config.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        self.training_data_dir = "./training_data"
        self.output_dir = "./output_lora"
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"Using device: {self.device}")
        
        if not DEPENDENCIES_AVAILABLE:
            logger.error("Required dependencies not available. Please install: pip install diffusers accelerate")
            return
    
    def check_training_data(self):
        """Check if training data is available"""
        images_dir = Path(f"{self.training_data_dir}/images")
        captions_dir = Path(f"{self.training_data_dir}/captions")
        
        if not images_dir.exists():
            logger.error(f"Images directory not found: {images_dir}")
            return False
        
        if not captions_dir.exists():
            logger.error(f"Captions directory not found: {captions_dir}")
            return False
        
        image_count = len(list(images_dir.glob("*.jpg"))) + len(list(images_dir.glob("*.png")))
        caption_count = len(list(captions_dir.glob("*.txt")))
        
        logger.info(f"Found {image_count} images and {caption_count} captions")
        
        if image_count == 0:
            logger.error("No training images found")
            return False
        
        return True
    
    def estimate_training_time(self, steps=2000):
        """Estimate training time based on GPU"""
        if "4060" in str(torch.cuda.get_device_name(0)) if torch.cuda.is_available() else False:
            # RTX 4060 Ti estimate: ~1.5-2 seconds per step
            estimated_hours = (steps * 1.75) / 3600
            return estimated_hours
        else:
            return "Unknown - depends on GPU"
    
    def create_simple_training_script(self):
        """Create a simplified training approach"""
        
        if not self.check_training_data():
            return False
        
        # Estimate training time
        estimated_time = self.estimate_training_time()
        
        logger.info("=" * 60)
        logger.info("🚀 LORA TRAINING SETUP")
        logger.info("=" * 60)
        logger.info(f"📁 Training data: {self.training_data_dir}")
        logger.info(f"📁 Output: {self.output_dir}")
        logger.info(f"🎯 Device: {self.device}")
        logger.info(f"⏱️ Estimated time: {estimated_time:.1f} hours" if isinstance(estimated_time, float) else f"⏱️ Estimated time: {estimated_time}")
        logger.info("=" * 60)
        
        # Create a practical training script
        training_script = f'''#!/usr/bin/env python3
"""
Practical LoRA Training Script
This script provides the framework for training but requires manual setup
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    packages = [
        "diffusers>=0.21.0",
        "accelerate>=0.20.0", 
        "transformers>=4.30.0",
        "xformers",
        "bitsandbytes"
    ]
    
    for package in packages:
        print(f"Installing {{package}}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])

def run_training():
    """Run the actual training"""
    
    # Training parameters
    params = {{
        "pretrained_model_name_or_path": "stabilityai/stable-diffusion-xl-base-1.0",
        "instance_data_dir": "{self.training_data_dir}/images",
        "output_dir": "{self.output_dir}",
        "instance_prompt": "lana pixie",
        "resolution": 768,
        "train_batch_size": 1,
        "gradient_accumulation_steps": 1,
        "learning_rate": 1e-4,
        "lr_scheduler": "constant",
        "lr_warmup_steps": 0,
        "max_train_steps": 2000,
        "checkpointing_steps": 500,
        "seed": 42,
        "mixed_precision": "fp16",
        "use_8bit_adam": True,
        "enable_xformers_memory_efficient_attention": True
    }}
    
    print("🎯 Training Parameters:")
    for key, value in params.items():
        print(f"  {{key}}: {{value}}")
    
    print("\\n⚠️ MANUAL TRAINING REQUIRED:")
    print("1. Install training dependencies (run install_requirements() first)")
    print("2. Use Kohya_ss, Dreambooth extension, or online training")
    print("3. Point training tool to prepared data")
    print("4. Use the parameters shown above")
    print("\\n✅ Training data is ready at: {self.training_data_dir}")

if __name__ == "__main__":
    print("🚀 Lana Pixie LoRA Training Setup")
    print("=" * 50)
    
    choice = input("Install requirements? (y/n): ")
    if choice.lower() == 'y':
        install_requirements()
    
    run_training()
'''
        
        # Save the training script
        os.makedirs(self.output_dir, exist_ok=True)
        script_path = f"{self.output_dir}/run_training.py"
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(training_script)
        
        logger.info(f"📄 Training script created: {script_path}")
        
        # Create a quick start guide
        guide = f'''# Lana Pixie LoRA Training Guide

## 🎯 Quick Start Options:

### Option 1: Use WebUI Dreambooth Extension (RECOMMENDED)
1. Start your WebUI with: `python launch.py --api`
2. Go to Dreambooth tab (should be available now)
3. Create new model:
   - Model Name: lana_pixie_v2
   - Source: stabilityai/stable-diffusion-xl-base-1.0
   - Instance Images: {self.training_data_dir}/images/
   - Instance Prompt: lana pixie
4. Train with settings:
   - Steps: 2000-3000
   - Learning Rate: 1e-4
   - Resolution: 768

### Option 2: Use Kohya_ss (Advanced)
1. Fix Kohya_ss installation
2. Point to training data: {self.training_data_dir}/
3. Use SDXL LoRA training settings

### Option 3: Online Training (Easiest)
1. Upload {self.training_data_dir}/ to Google Colab/RunPod
2. Use any SDXL LoRA training notebook
3. Download trained model

## 📊 Expected Results:
- Training time: ~{estimated_time:.1f} hours on RTX 4060 Ti
- Output: lana_pixie_v2.safetensors
- Improved consistency with 98 training images

## 🎯 Training Data Ready:
- Images: {self.training_data_dir}/images/ (98 files, 768x768)
- Captions: {self.training_data_dir}/captions/ (98 files)
- Trigger: "lana pixie"
'''
        
        guide_path = f"{self.output_dir}/TRAINING_GUIDE.md"
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide)
        
        logger.info(f"📖 Training guide created: {guide_path}")
        
        return True

def main():
    parser = argparse.ArgumentParser(description="Direct LoRA Training Setup")
    parser.add_argument("--config", default="config.json", help="Configuration file")
    
    args = parser.parse_args()
    
    trainer = DirectLoRATrainer(args.config)
    
    if not DEPENDENCIES_AVAILABLE:
        logger.error("❌ Missing dependencies. Cannot proceed with training.")
        logger.info("💡 Use the prepared training data with external tools instead.")
        return False
    
    success = trainer.create_simple_training_script()
    
    if success:
        logger.info("✅ Training setup complete!")
        logger.info("📋 Next: Choose a training method from the guide")
    
    return success

if __name__ == "__main__":
    main()
