#!/usr/bin/env python3
"""
Simple LoRA Training Script for Lana Pixie
Uses WebUI's existing environment and libraries
"""

import os
import sys
import json
import shutil
from pathlib import Path
from PIL import Image
import argparse

class SimpleLORATrainer:
    def __init__(self, config_path="../config.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        self.ref_images_dir = self.config.get("ref_images_dir")
        self.output_dir = "./training_data"
        self.lora_output_dir = "./output_lora"
        
    def prepare_training_data(self):
        """Prepare images and captions for training"""
        print("🔄 Preparing training data...")
        
        # Create directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(f"{self.output_dir}/images", exist_ok=True)
        os.makedirs(f"{self.output_dir}/captions", exist_ok=True)
        
        ref_dir = Path(self.ref_images_dir)
        if not ref_dir.exists():
            print(f"❌ Reference directory not found: {ref_dir}")
            return False
        
        # Get all image files
        extensions = ['.png', '.jpg', '.jpeg', '.webp']
        images = []
        for ext in extensions:
            images.extend(list(ref_dir.glob(f"*{ext}")))
            images.extend(list(ref_dir.glob(f"*{ext.upper()}")))
        
        print(f"📸 Found {len(images)} reference images")
        
        processed_count = 0
        for i, img_path in enumerate(images):
            try:
                # Process image
                processed_path = self.process_image(img_path, i)
                if processed_path:
                    # Create caption
                    self.create_caption(processed_path, i)
                    processed_count += 1
                    
            except Exception as e:
                print(f"⚠️ Error processing {img_path}: {e}")
                continue
        
        print(f"✅ Processed {processed_count} images for training")
        return processed_count > 0
    
    def process_image(self, image_path, index):
        """Process and resize image for training"""
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if needed
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize to training resolution (768x768 for SDXL)
                target_size = 768
                
                # Calculate crop to maintain aspect ratio
                width, height = img.size
                if width > height:
                    # Landscape - crop width
                    new_width = int(height * target_size / target_size)
                    left = (width - new_width) // 2
                    img = img.crop((left, 0, left + new_width, height))
                else:
                    # Portrait - crop height
                    new_height = int(width * target_size / target_size)
                    top = (height - new_height) // 2
                    img = img.crop((0, top, width, top + new_height))
                
                # Resize to target
                img = img.resize((target_size, target_size), Image.Resampling.LANCZOS)
                
                # Save processed image
                output_path = f"{self.output_dir}/images/{index:04d}_lana_pixie.jpg"
                img.save(output_path, "JPEG", quality=95)
                
                return output_path
                
        except Exception as e:
            print(f"❌ Error processing image {image_path}: {e}")
            return None
    
    def create_caption(self, image_path, index):
        """Create caption file for training"""
        # Extract filename info for better captions
        filename = Path(image_path).stem
        
        # Base caption with trigger word
        base_caption = "lana pixie, "
        
        # Add descriptive elements based on filename or use generic
        if "portrait" in filename.lower():
            caption = base_caption + "portrait, beautiful woman, detailed face, high quality"
        elif "full" in filename.lower() or "body" in filename.lower():
            caption = base_caption + "full body, beautiful woman, detailed, high quality"
        elif "close" in filename.lower():
            caption = base_caption + "close-up, beautiful woman, detailed face, high quality"
        else:
            caption = base_caption + "beautiful woman, detailed, high quality"
        
        # Save caption file
        caption_path = f"{self.output_dir}/captions/{index:04d}_lana_pixie.txt"
        with open(caption_path, 'w') as f:
            f.write(caption)
        
        return caption_path
    
    def create_training_config(self):
        """Create training configuration file"""
        config = {
            "model_name": "lana_pixie_v2",
            "base_model": "stabilityai/stable-diffusion-xl-base-1.0",
            "instance_data_dir": f"{self.output_dir}/images",
            "instance_prompt": "lana pixie",
            "resolution": 768,
            "train_batch_size": 1,
            "gradient_accumulation_steps": 1,
            "learning_rate": 1e-4,
            "lr_scheduler": "constant",
            "lr_warmup_steps": 0,
            "max_train_steps": 2000,
            "checkpointing_steps": 500,
            "seed": 42,
            "output_dir": self.lora_output_dir,
            "rank": 32,
            "alpha": 32
        }
        
        config_path = f"{self.output_dir}/training_config.json"
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"📄 Training config saved: {config_path}")
        return config_path
    
    def generate_training_script(self):
        """Generate a training script that can be run manually"""
        script_content = f'''#!/bin/bash
# Lana Pixie LoRA Training Script
# Run this in the WebUI environment

echo "🚀 Starting Lana Pixie LoRA Training..."

# Set environment variables
export MODEL_NAME="lana_pixie_v2"
export INSTANCE_DIR="{self.output_dir}/images"
export OUTPUT_DIR="{self.lora_output_dir}"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Training command (you'll need to adapt this to your specific training setup)
echo "📝 Training configuration:"
echo "  - Images: {self.output_dir}/images"
echo "  - Output: {self.lora_output_dir}"
echo "  - Steps: 2000"
echo "  - Learning Rate: 1e-4"
echo "  - Resolution: 768x768"

echo ""
echo "⚠️ Manual training required:"
echo "1. Use your preferred LoRA training tool (Kohya_ss, Dreambooth, etc.)"
echo "2. Point it to the prepared images in: {self.output_dir}/images"
echo "3. Use the captions in: {self.output_dir}/captions"
echo "4. Set trigger word: 'lana pixie'"
echo "5. Train for 2000-3000 steps at 1e-4 learning rate"

echo ""
echo "✅ Training data preparation complete!"
'''
        
        script_path = f"{self.output_dir}/train_lora.sh"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)

        # Also create a Windows batch file
        batch_content = f'''@echo off
echo Starting Lana Pixie LoRA Training...

echo 📝 Training configuration:
echo   - Images: {self.output_dir}/images
echo   - Output: {self.lora_output_dir}
echo   - Steps: 2000
echo   - Learning Rate: 1e-4
echo   - Resolution: 768x768

echo.
echo ⚠️ Manual training required:
echo 1. Use your preferred LoRA training tool (Kohya_ss, Dreambooth, etc.)
echo 2. Point it to the prepared images in: {self.output_dir}/images
echo 3. Use the captions in: {self.output_dir}/captions
echo 4. Set trigger word: 'lana pixie'
echo 5. Train for 2000-3000 steps at 1e-4 learning rate

echo.
echo ✅ Training data preparation complete!
pause
'''
        
        batch_path = f"{self.output_dir}/train_lora.bat"
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"📄 Training scripts created:")
        print(f"   - {script_path}")
        print(f"   - {batch_path}")
        
        return script_path, batch_path
    
    def prepare_for_training(self):
        """Complete preparation workflow"""
        print("🎯 Preparing Lana Pixie LoRA Training")
        print("=" * 50)
        
        # Step 1: Prepare training data
        if not self.prepare_training_data():
            print("❌ Failed to prepare training data")
            return False
        
        # Step 2: Create training config
        self.create_training_config()
        
        # Step 3: Generate training scripts
        self.generate_training_script()
        
        # Summary
        print("\n🎉 Training preparation complete!")
        print(f"📁 Training data: {self.output_dir}")
        print(f"📁 Output directory: {self.lora_output_dir}")
        print("\n📋 Next steps:")
        print("1. Review the prepared images and captions")
        print("2. Use your preferred LoRA training tool")
        print("3. Point it to the prepared training data")
        print("4. Train with the recommended settings")
        
        return True

def main():
    parser = argparse.ArgumentParser(description="Prepare Lana Pixie LoRA Training Data")
    parser.add_argument("--config", default="../config.json", help="Configuration file path")
    
    args = parser.parse_args()
    
    trainer = SimpleLORATrainer(args.config)
    success = trainer.prepare_for_training()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
