{"metadata": {"total_size": 9524621312}, "weight_map": {"encoder.block.0.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.0.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.0.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.0.layer.0.SelfAttention.relative_attention_bias.weight": "model-00001-of-00002.safetensors", "encoder.block.0.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.0.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.0.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.0.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.0.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.0.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.1.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.1.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.1.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.1.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.1.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.1.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.1.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.1.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.1.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.10.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.10.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.10.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.10.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.10.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.10.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.10.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.10.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.10.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.11.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.11.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.11.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.11.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.11.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.11.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.11.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.11.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.11.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.12.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.12.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.12.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.12.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.12.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.12.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.12.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.12.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.12.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.13.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.13.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.13.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.13.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.13.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.13.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.13.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.13.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.13.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.14.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.14.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.14.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.14.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.14.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.14.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.14.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.14.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.14.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.15.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.15.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.15.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.15.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.15.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.15.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.15.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.15.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.15.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.16.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.16.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.16.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.16.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.16.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.16.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.16.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.16.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.16.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.17.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.17.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.17.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.17.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.17.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.17.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.17.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.17.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.17.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.18.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.18.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.18.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.18.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.18.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.18.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.18.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.18.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.18.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.19.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.19.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.19.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.19.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.19.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.19.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.19.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.19.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.19.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.2.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.2.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.2.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.2.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.2.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.2.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.2.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.2.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.2.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.20.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.20.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.20.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.20.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.20.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.20.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.20.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.20.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.20.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.21.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.21.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.21.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.21.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.21.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.21.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.21.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.21.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.21.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.22.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.22.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.22.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.22.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.22.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.22.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.22.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.22.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.22.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.23.layer.0.SelfAttention.k.weight": "model-00002-of-00002.safetensors", "encoder.block.23.layer.0.SelfAttention.o.weight": "model-00002-of-00002.safetensors", "encoder.block.23.layer.0.SelfAttention.q.weight": "model-00002-of-00002.safetensors", "encoder.block.23.layer.0.SelfAttention.v.weight": "model-00002-of-00002.safetensors", "encoder.block.23.layer.0.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.23.layer.1.DenseReluDense.wi_0.weight": "model-00002-of-00002.safetensors", "encoder.block.23.layer.1.DenseReluDense.wi_1.weight": "model-00002-of-00002.safetensors", "encoder.block.23.layer.1.DenseReluDense.wo.weight": "model-00002-of-00002.safetensors", "encoder.block.23.layer.1.layer_norm.weight": "model-00002-of-00002.safetensors", "encoder.block.3.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.3.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.3.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.3.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.3.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.3.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.3.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.3.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.3.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.4.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.4.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.4.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.4.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.4.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.4.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.4.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.4.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.4.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.5.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.5.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.5.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.5.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.5.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.5.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.5.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.5.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.5.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.6.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.6.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.6.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.6.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.6.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.6.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.6.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.6.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.6.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.7.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.7.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.7.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.7.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.7.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.7.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.7.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.7.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.7.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.8.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.8.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.8.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.8.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.8.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.8.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.8.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.8.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.8.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.9.layer.0.SelfAttention.k.weight": "model-00001-of-00002.safetensors", "encoder.block.9.layer.0.SelfAttention.o.weight": "model-00001-of-00002.safetensors", "encoder.block.9.layer.0.SelfAttention.q.weight": "model-00001-of-00002.safetensors", "encoder.block.9.layer.0.SelfAttention.v.weight": "model-00001-of-00002.safetensors", "encoder.block.9.layer.0.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.block.9.layer.1.DenseReluDense.wi_0.weight": "model-00001-of-00002.safetensors", "encoder.block.9.layer.1.DenseReluDense.wi_1.weight": "model-00001-of-00002.safetensors", "encoder.block.9.layer.1.DenseReluDense.wo.weight": "model-00001-of-00002.safetensors", "encoder.block.9.layer.1.layer_norm.weight": "model-00001-of-00002.safetensors", "encoder.final_layer_norm.weight": "model-00002-of-00002.safetensors", "shared.weight": "model-00001-of-00002.safetensors"}}