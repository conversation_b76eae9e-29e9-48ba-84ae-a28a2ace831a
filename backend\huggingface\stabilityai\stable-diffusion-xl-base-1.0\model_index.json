{"_class_name": "StableDiffusionXLPipeline", "_diffusers_version": "0.19.0.dev0", "force_zeros_for_empty_prompt": true, "add_watermarker": null, "scheduler": ["diffusers", "EulerDiscreteScheduler"], "text_encoder": ["transformers", "CLIPTextModel"], "text_encoder_2": ["transformers", "CLIPTextModelWithProjection"], "tokenizer": ["transformers", "CLIPTokenizer"], "tokenizer_2": ["transformers", "CLIPTokenizer"], "unet": ["diffusers", "UNet2DConditionModel"], "vae": ["diffusers", "AutoencoderKL"]}