#!/usr/bin/env python3
"""
Lana Pixie Consistent Character Pipeline
Batch generation script for SFW/NSFW content using Stable Diffusion + FaceFusion
"""

import os
import csv
import json
import time
import random
import hashlib
import subprocess
from datetime import datetime
from pathlib import Path
import requests

class LanaPixiePipeline:
    def __init__(self, config_path="config.json"):
        self.config = self.load_config(config_path)
        self.setup_directories()
        
    def load_config(self, config_path):
        """Load configuration from JSON file"""
        default_config = {
            "webui_url": "http://127.0.0.1:7860",
            "lora_name": "lana_pixie_sdxl_improved",
            "lora_strength": 0.8,
            "base_model": "cyberrealisticXL_v60.safetensors",
            "output_dir": "./output",
            "ref_images_dir": "./ref_images", 
            "batch_size": 4,
            "steps": 30,
            "cfg_scale": 7.0,
            "width": 768,
            "height": 1024,
            "sampler": "DPM++ 2M Karras",
            "facefusion_strength": 0.3,
            "quality_checks": {
                "max_file_size_mb": 10,
                "min_face_confidence": 0.8,
                "check_hands": True,
                "check_eyes": True
            }
        }
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        else:
            # Create default config file
            with open(config_path, 'w') as f:
                json.dump(default_config, f, indent=2)
                
        return default_config
    
    def setup_directories(self):
        """Create necessary directories"""
        dirs = [
            self.config["output_dir"],
            f"{self.config['output_dir']}/sfw",
            f"{self.config['output_dir']}/nsfw", 
            f"{self.config['output_dir']}/archive",
            "./prompts",
            "./logs"
        ]
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    def generate_prompts(self, count=100, sfw_ratio=0.5):
        """Generate batch prompts for SFW/NSFW content"""
        
        # Base prompt components
        identity_token = f"<lora:{self.config['lora_name']}:{self.config['lora_strength']}>"
        
        sfw_styles = [
            "soft daylight bedroom, cozy knit sweater, natural makeup, candid",
            "golden hour lighting, casual outfit, warm smile, relaxed pose",
            "studio lighting, elegant dress, professional headshot",
            "natural window light, reading book, comfortable setting",
            "outdoor portrait, summer dress, gentle breeze, happy expression"
        ]
        
        nsfw_styles = [
            "dim golden hour, lace lingerie, seductive pose, cinematic lighting",
            "bedroom setting, silk sheets, intimate lighting, artistic nude",
            "boudoir photography, soft shadows, elegant pose, sensual mood",
            "warm candlelight, romantic setting, intimate portrait",
            "studio lighting, artistic composition, tasteful nude, dramatic shadows"
        ]
        
        quality_tags = "35mm photo, f1.8, photorealistic, high quality, detailed"
        
        prompts = []
        sfw_count = int(count * sfw_ratio)
        
        # Generate SFW prompts
        for i in range(sfw_count):
            style = random.choice(sfw_styles)
            prompt = f"{identity_token}, {style}, {quality_tags}"
            prompts.append({
                "id": f"sfw_{i+1:03d}",
                "prompt": prompt,
                "negative": "blurry, low quality, distorted, deformed, extra limbs, bad anatomy",
                "type": "sfw"
            })
        
        # Generate NSFW prompts  
        for i in range(count - sfw_count):
            style = random.choice(nsfw_styles)
            prompt = f"{identity_token}, {style}, {quality_tags}"
            prompts.append({
                "id": f"nsfw_{i+1:03d}",
                "prompt": prompt,
                "negative": "blurry, low quality, distorted, deformed, extra limbs, bad anatomy, censored",
                "type": "nsfw"
            })
        
        return prompts
    
    def save_prompts_csv(self, prompts, filename="batch_prompts.csv"):
        """Save prompts to CSV file for batch processing"""
        csv_path = f"./prompts/{filename}"
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(["id", "prompt", "negative_prompt", "type"])
            for p in prompts:
                writer.writerow([p["id"], p["prompt"], p["negative"], p["type"]])
        return csv_path
    
    def generate_batch_via_api(self, prompts):
        """Generate images using WebUI API"""
        results = []
        
        for prompt_data in prompts:
            payload = {
                "prompt": prompt_data["prompt"],
                "negative_prompt": prompt_data["negative"],
                "steps": self.config["steps"],
                "cfg_scale": self.config["cfg_scale"],
                "width": self.config["width"],
                "height": self.config["height"],
                "sampler_name": self.config["sampler"],
                "batch_size": 1,
                "save_images": True,
                "send_images": True
            }
            
            try:
                response = requests.post(f"{self.config['webui_url']}/sdapi/v1/txt2img", 
                                       json=payload, timeout=300)
                
                if response.status_code == 200:
                    result = response.json()
                    # Save image with metadata
                    self.save_generated_image(result, prompt_data)
                    results.append({"status": "success", "data": prompt_data})
                else:
                    results.append({"status": "error", "data": prompt_data, "error": response.text})
                    
            except Exception as e:
                results.append({"status": "error", "data": prompt_data, "error": str(e)})
                
            # Small delay between generations
            time.sleep(2)
            
        return results
    
    def save_generated_image(self, api_result, prompt_data):
        """Save generated image with proper naming and metadata"""
        import base64
        from PIL import Image
        from io import BytesIO
        
        # Decode base64 image
        image_data = base64.b64decode(api_result["images"][0])
        image = Image.open(BytesIO(image_data))
        
        # Generate filename with seed and hash
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        prompt_hash = hashlib.md5(prompt_data["prompt"].encode()).hexdigest()[:8]
        filename = f"{prompt_data['id']}_{timestamp}_{prompt_hash}.png"
        
        # Save to appropriate directory
        output_dir = f"{self.config['output_dir']}/{prompt_data['type']}"
        filepath = f"{output_dir}/{filename}"
        
        image.save(filepath)
        
        # Save metadata
        metadata = {
            "prompt": prompt_data["prompt"],
            "negative_prompt": prompt_data["negative"],
            "timestamp": timestamp,
            "filename": filename,
            "type": prompt_data["type"],
            "config": self.config
        }
        
        metadata_path = f"{output_dir}/{filename.replace('.png', '_metadata.json')}"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
            
        return filepath
    
    def quality_check(self, image_path):
        """Perform quality checks on generated image"""
        checks = {
            "file_size_ok": False,
            "face_detected": False,
            "hands_ok": False,
            "eyes_ok": False,
            "overall_pass": False
        }
        
        # File size check
        file_size_mb = os.path.getsize(image_path) / (1024 * 1024)
        checks["file_size_ok"] = file_size_mb <= self.config["quality_checks"]["max_file_size_mb"]
        
        # TODO: Implement face detection, hand checking, eye checking
        # For now, assume basic checks pass
        checks["face_detected"] = True
        checks["hands_ok"] = True  
        checks["eyes_ok"] = True
        
        checks["overall_pass"] = all([
            checks["file_size_ok"],
            checks["face_detected"],
            checks["hands_ok"],
            checks["eyes_ok"]
        ])
        
        return checks
    
    def run_batch_generation(self, prompt_count=50, sfw_ratio=0.6):
        """Run complete batch generation pipeline"""
        print(f"🚀 Starting Lana Pixie batch generation...")
        print(f"📊 Generating {prompt_count} prompts ({int(prompt_count * sfw_ratio)} SFW, {prompt_count - int(prompt_count * sfw_ratio)} NSFW)")
        
        # Generate prompts
        prompts = self.generate_prompts(prompt_count, sfw_ratio)
        csv_path = self.save_prompts_csv(prompts)
        print(f"💾 Prompts saved to: {csv_path}")
        
        # Generate images
        print(f"🎨 Starting image generation...")
        results = self.generate_batch_via_api(prompts)
        
        # Quality check results
        passed = sum(1 for r in results if r["status"] == "success")
        failed = len(results) - passed
        
        print(f"✅ Generation complete: {passed} successful, {failed} failed")
        
        return results

if __name__ == "__main__":
    pipeline = LanaPixiePipeline()
    results = pipeline.run_batch_generation(prompt_count=20, sfw_ratio=0.7)
