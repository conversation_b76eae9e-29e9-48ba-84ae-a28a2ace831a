# Lana Pixie LoRA Training Guide

## 🎯 Quick Start Options:

### Option 1: Use WebUI Dreambooth Extension (RECOMMENDED)
1. Start your WebUI with: `python launch.py --api`
2. Go to Dreambooth tab (should be available now)
3. Create new model:
   - Model Name: lana_pixie_v2
   - Source: stabilityai/stable-diffusion-xl-base-1.0
   - Instance Images: ./training_data/images/
   - Instance Prompt: lana pixie
4. Train with settings:
   - Steps: 2000-3000
   - Learning Rate: 1e-4
   - Resolution: 768

### Option 2: Use Kohya_ss (Advanced)
1. Fix Kohya_ss installation
2. Point to training data: ./training_data/
3. Use SDXL LoRA training settings

### Option 3: Online Training (Easiest)
1. Upload ./training_data/ to Google Colab/RunPod
2. Use any SDXL LoRA training notebook
3. Download trained model

## 📊 Expected Results:
- Training time: ~1.0 hours on RTX 4060 Ti
- Output: lana_pixie_v2.safetensors
- Improved consistency with 98 training images

## 🎯 Training Data Ready:
- Images: ./training_data/images/ (98 files, 768x768)
- Captions: ./training_data/captions/ (98 files)
- Trigger: "lana pixie"
